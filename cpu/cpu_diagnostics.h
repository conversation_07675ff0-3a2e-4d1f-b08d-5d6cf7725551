#pragma once

#include <mutex>
#include <string>
#include <unordered_map>

#include "ps4/ps4_emulator.h"
#include "x86_64_cpu.h"


namespace x86_64 {

// IMPROVEMENT: Enhanced diagnostic metrics
enum class DiagnosticLevel {
  Basic,    // Essential metrics only
  Detailed, // Include performance counters
  Verbose   // Include all debug information
};

/**
 * @brief Singleton class for collecting and reporting CPU diagnostic metrics.
 * IMPROVEMENT: Enhanced with performance monitoring and configurable detail
 * levels.
 */
class CPUDiagnostics {
public:
  /**
   * @brief Retrieves the singleton instance of CPUDiagnostics.
   * @return Reference to the singleton instance.
   */
  static CPUDiagnostics &GetInstance();

  /**
   * @brief Updates CPU-related diagnostic metrics.
   * @details Collects metrics from all CPUs, pipelines, APIC, fibers, and
   * caches.
   * @param level Detail level for metrics collection
   */
  void UpdateMetrics(DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief Updates CPU-related diagnostic metrics with provided data.
   * @details Collects metrics from provided CPU data to break circular
   * dependency.
   * @param cpus Vector of CPU pointers to collect metrics from
   * @param fiberManager Reference to fiber manager for fiber count
   * @param level Detail level for metrics collection
   */
  void UpdateMetricsWithData(const std::vector<class X86_64CPU *> &cpus,
                             const class ps4::FiberManager &fiberManager,
                             DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief Resets all diagnostic metrics to default values.
   */
  void ResetMetrics();

  /**
   * @brief Retrieves the current diagnostic metrics.
   * @param level Detail level for returned metrics
   * @return Map of metric names to values.
   */
  std::unordered_map<std::string, uint64_t>
  GetMetrics(DiagnosticLevel level = DiagnosticLevel::Detailed) const;

  /**
   * @brief Logs detailed diagnostic information to spdlog.
   * @param level Detail level for logging
   */
  void LogDiagnostics(DiagnosticLevel level = DiagnosticLevel::Detailed);

  /**
   * @brief IMPROVEMENT: Get performance summary
   * @return Human-readable performance summary
   */
  std::string GetPerformanceSummary() const;

  /**
   * @brief IMPROVEMENT: Export metrics to JSON format
   * @return JSON string containing all metrics
   */
  std::string ExportToJSON() const;

  /**
   * @brief IMPROVEMENT: Set update interval for automatic metrics collection
   * @param intervalMs Update interval in milliseconds
   */
  void SetUpdateInterval(uint32_t intervalMs);

  /**
   * @brief Record an instruction for diagnostic tracking
   * @param instruction The decoded instruction to record
   */
  void RecordInstruction(const struct DecodedInstruction& instruction);

  /**
   * @brief Get recent instructions from history
   * @param count Number of recent instructions to retrieve
   * @return Vector of recent instructions
   */
  std::vector<struct DecodedInstruction> GetRecentInstructions(size_t count) const;

private:
  CPUDiagnostics();
  ~CPUDiagnostics() = default;

  // Prevent copying
  CPUDiagnostics(const CPUDiagnostics &) = delete;
  CPUDiagnostics &operator=(const CPUDiagnostics &) = delete;

  void CollectBasicMetrics();
  void CollectDetailedMetrics();
  void CollectVerboseMetrics();

  // Verbose metrics collection helpers
  void CollectInstructionTraces(const std::vector<class X86_64CPU *> &cpus);
  void CollectPipelineDetailedStats(const std::vector<class X86_64CPU *> &cpus);
  void
  CollectRegisterDependencyStats(const std::vector<class X86_64CPU *> &cpus);
  void CollectCacheDetailedStats(const std::vector<class X86_64CPU *> &cpus);
  void CollectBranchPredictionStats(const std::vector<class X86_64CPU *> &cpus);
  void CollectMemorySubsystemStats(const std::vector<class X86_64CPU *> &cpus);
  void CollectPerformanceCounters(const std::vector<class X86_64CPU *> &cpus);

  mutable std::mutex m_mutex;
  std::unordered_map<std::string, uint64_t> m_metrics;
  std::unordered_map<std::string, double> m_performance_metrics;
  std::unordered_map<std::string, std::string>
      m_trace_data;                     // For instruction traces
  uint32_t m_update_interval_ms = 1000; // Default 1 second
  std::chrono::steady_clock::time_point m_last_update;

  // Verbose metrics storage
  struct InstructionTrace {
    uint64_t pc;
    uint32_t instruction_type;
    uint64_t cycle;
    uint32_t stage;
    std::string operands;
    uint64_t execution_time_ns;
  };

  struct PipelineStageStats {
    uint64_t fetch_stalls;
    uint64_t decode_stalls;
    uint64_t execute_stalls;
    uint64_t memory_stalls;
    uint64_t writeback_stalls;
    double fetch_utilization;
    double decode_utilization;
    double execute_utilization;
    double memory_utilization;
    double writeback_utilization;
  };

  // Opcode statistics structure (moved before usage)
  struct OpcodeStats {
    uint64_t count = 0;
    uint64_t total_cycles = 0;
  };

  std::vector<InstructionTrace> m_instruction_traces;
  PipelineStageStats m_pipeline_stage_stats;
  uint32_t m_max_trace_entries = 1000; // Limit trace buffer size

  // Instruction recording members
  std::vector<struct DecodedInstruction> m_instruction_history;
  std::vector<OpcodeStats> m_opcode_stats;

  // Constants for instruction tracking
  static constexpr size_t MAX_OPCODES = 1024;
  static constexpr size_t MAX_HISTORY_SIZE = 10000;
};

} // namespace x86_64