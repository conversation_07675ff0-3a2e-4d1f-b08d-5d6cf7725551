// Copyright 2025 <Copyright Owner>

#include "tile_manager.h"
#include "../debug/vector_debug.h"
#include "../common/lock_ordering.h"

#include <algorithm>
#include <cstring>
#include <stdexcept>

#include <fmt/core.h>
#include <spdlog/spdlog.h>

namespace ps4 {

TileManager::TileManager(size_t max_tiles, size_t tile_width, size_t tile_height)
    : m_max_tiles(max_tiles), m_tile_width(tile_width), m_tile_height(tile_height) {
    
    if (max_tiles == 0) {
        throw std::invalid_argument("TileManager: max_tiles cannot be zero");
    }
    if (tile_width == 0 || tile_height == 0) {
        throw std::invalid_argument("TileManager: tile dimensions cannot be zero");
    }
    
    m_tiles.reserve(max_tiles);
    m_tile_cache.reserve(max_tiles);
    m_active_tiles.reserve(max_tiles);
    
    spdlog::info("TileManager initialized: max_tiles={}, tile_size={}x{}", 
                 max_tiles, tile_width, tile_height);
}

bool TileManager::AllocateTile(uint32_t tile_id, const TileDescriptor& descriptor) {
    MEMORY_LOCK(m_mutex, "TileManagerMutex");
    
    // Validate tile_id bounds
    if (tile_id >= m_max_tiles) {
        spdlog::error("TileManager::AllocateTile: tile_id {} >= max_tiles {}", 
                      tile_id, m_max_tiles);
        return false;
    }
    
    // Validate descriptor
    if (descriptor.width == 0 || descriptor.height == 0) {
        spdlog::error("TileManager::AllocateTile: Invalid tile dimensions {}x{}", 
                      descriptor.width, descriptor.height);
        return false;
    }
    
    // Ensure tiles vector is large enough
    if (m_tiles.size() <= tile_id) {
        m_tiles.resize(tile_id + 1);
    }
    
    // Safe access to tile
    auto& tile = CRITICAL_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::AllocateTile");
    
    if (tile.allocated) {
        spdlog::warn("TileManager::AllocateTile: Tile {} already allocated, deallocating first", tile_id);
        DeallocateTileInternal(tile_id);
    }
    
    try {
        // Calculate required memory size with overflow check
        size_t pixel_size = descriptor.bits_per_pixel / 8;
        if (pixel_size == 0) pixel_size = 1;
        
        size_t total_pixels = descriptor.width * descriptor.height;
        if (total_pixels / descriptor.width != descriptor.height) {
            spdlog::error("TileManager::AllocateTile: Integer overflow in tile size calculation");
            return false;
        }
        
        size_t total_size = total_pixels * pixel_size;
        if (total_size / total_pixels != pixel_size) {
            spdlog::error("TileManager::AllocateTile: Integer overflow in memory size calculation");
            return false;
        }
        
        tile.data.resize(total_size);
        tile.descriptor = descriptor;
        tile.allocated = true;
        tile.last_access = std::chrono::steady_clock::now();
        
        m_active_tiles.push_back(tile_id);
        
        spdlog::debug("TileManager::AllocateTile: Allocated tile {} ({}x{}, {} bytes)", 
                      tile_id, descriptor.width, descriptor.height, total_size);
        
        return true;
        
    } catch (const std::exception& e) {
        spdlog::error("TileManager::AllocateTile: Failed to allocate tile {}: {}", tile_id, e.what());
        return false;
    }
}

bool TileManager::DeallocateTile(uint32_t tile_id) {
    MEMORY_LOCK(m_mutex, "TileManagerMutex");
    return DeallocateTileInternal(tile_id);
}

bool TileManager::DeallocateTileInternal(uint32_t tile_id) {
    // Validate tile_id bounds
    if (tile_id >= m_max_tiles || tile_id >= m_tiles.size()) {
        spdlog::error("TileManager::DeallocateTileInternal: tile_id {} out of bounds (max: {}, size: {})", 
                      tile_id, m_max_tiles, m_tiles.size());
        return false;
    }
    
    auto& tile = CRITICAL_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::DeallocateTileInternal");
    
    if (!tile.allocated) {
        spdlog::warn("TileManager::DeallocateTileInternal: Tile {} not allocated", tile_id);
        return false;
    }
    
    tile.data.clear();
    tile.data.shrink_to_fit();
    tile.allocated = false;
    
    // Remove from active tiles list with bounds checking
    auto it = std::find(m_active_tiles.begin(), m_active_tiles.end(), tile_id);
    if (it != m_active_tiles.end()) {
        m_active_tiles.erase(it);
    }
    
    spdlog::debug("TileManager::DeallocateTileInternal: Deallocated tile {}", tile_id);
    return true;
}

bool TileManager::WriteTileData(uint32_t tile_id, const void* data, size_t size, 
                               size_t offset) {
    MEMORY_SHARED_LOCK(m_mutex, "TileManagerMutex");
    
    // Validate parameters
    if (!data) {
        spdlog::error("TileManager::WriteTileData: data pointer is null");
        return false;
    }
    
    if (tile_id >= m_max_tiles || tile_id >= m_tiles.size()) {
        spdlog::error("TileManager::WriteTileData: tile_id {} out of bounds", tile_id);
        return false;
    }
    
    auto& tile = CRITICAL_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::WriteTileData");
    
    if (!tile.allocated) {
        spdlog::error("TileManager::WriteTileData: Tile {} not allocated", tile_id);
        return false;
    }
    
    // Check bounds for write operation
    if (offset >= tile.data.size()) {
        spdlog::error("TileManager::WriteTileData: offset {} >= tile data size {}", 
                      offset, tile.data.size());
        return false;
    }
    
    if (offset + size > tile.data.size()) {
        spdlog::error("TileManager::WriteTileData: write would exceed tile bounds (offset: {}, size: {}, tile_size: {})", 
                      offset, size, tile.data.size());
        return false;
    }
    
    // Perform safe copy
    std::memcpy(tile.data.data() + offset, data, size);
    tile.last_access = std::chrono::steady_clock::now();
    
    spdlog::trace("TileManager::WriteTileData: Wrote {} bytes to tile {} at offset {}", 
                  size, tile_id, offset);
    return true;
}

bool TileManager::ReadTileData(uint32_t tile_id, void* data, size_t size, 
                              size_t offset) const {
    MEMORY_SHARED_LOCK(m_mutex, "TileManagerMutex");
    
    // Validate parameters
    if (!data) {
        spdlog::error("TileManager::ReadTileData: data pointer is null");
        return false;
    }
    
    if (tile_id >= m_max_tiles || tile_id >= m_tiles.size()) {
        spdlog::error("TileManager::ReadTileData: tile_id {} out of bounds", tile_id);
        return false;
    }
    
    const auto& tile = CRITICAL_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::ReadTileData");
    
    if (!tile.allocated) {
        spdlog::error("TileManager::ReadTileData: Tile {} not allocated", tile_id);
        return false;
    }
    
    // Check bounds for read operation
    if (offset >= tile.data.size()) {
        spdlog::error("TileManager::ReadTileData: offset {} >= tile data size {}", 
                      offset, tile.data.size());
        return false;
    }
    
    if (offset + size > tile.data.size()) {
        spdlog::error("TileManager::ReadTileData: read would exceed tile bounds (offset: {}, size: {}, tile_size: {})", 
                      offset, size, tile.data.size());
        return false;
    }
    
    // Perform safe copy
    std::memcpy(data, tile.data.data() + offset, size);
    
    // Update access time (const_cast for statistics)
    const_cast<Tile&>(tile).last_access = std::chrono::steady_clock::now();
    
    spdlog::trace("TileManager::ReadTileData: Read {} bytes from tile {} at offset {}", 
                  size, tile_id, offset);
    return true;
}

std::vector<uint32_t> TileManager::GetActiveTiles() const {
    MEMORY_SHARED_LOCK(m_mutex, "TileManagerMutex");
    return m_active_tiles;
}

void TileManager::CleanupUnusedTiles(std::chrono::milliseconds max_age) {
    MEMORY_LOCK(m_mutex, "TileManagerMutex");
    
    auto now = std::chrono::steady_clock::now();
    std::vector<uint32_t> tiles_to_cleanup;
    
    // Find tiles to cleanup with bounds checking
    for (size_t i = 0; i < m_active_tiles.size(); ++i) {
        uint32_t tile_id = SAFE_VECTOR_ACCESS(m_active_tiles, i, "TileManager::CleanupUnusedTiles");
        
        if (tile_id < m_tiles.size()) {
            const auto& tile = SAFE_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::CleanupUnusedTiles tile access");
            
            if (tile.allocated) {
                auto age = std::chrono::duration_cast<std::chrono::milliseconds>(now - tile.last_access);
                if (age > max_age) {
                    tiles_to_cleanup.push_back(tile_id);
                }
            }
        }
    }
    
    // Cleanup identified tiles
    for (uint32_t tile_id : tiles_to_cleanup) {
        DeallocateTileInternal(tile_id);
        spdlog::debug("TileManager::CleanupUnusedTiles: Cleaned up tile {}", tile_id);
    }
    
    if (!tiles_to_cleanup.empty()) {
        spdlog::info("TileManager::CleanupUnusedTiles: Cleaned up {} tiles", tiles_to_cleanup.size());
    }
}

TileManager::TileStats TileManager::GetStats() const {
    MEMORY_SHARED_LOCK(m_mutex, "TileManagerMutex");
    
    TileStats stats{};
    stats.total_tiles = m_max_tiles;
    stats.allocated_tiles = m_active_tiles.size();
    stats.free_tiles = m_max_tiles - m_active_tiles.size();
    
    // Calculate memory usage with bounds checking
    for (size_t i = 0; i < m_active_tiles.size(); ++i) {
        uint32_t tile_id = SAFE_VECTOR_ACCESS(m_active_tiles, i, "TileManager::GetStats");
        
        if (tile_id < m_tiles.size()) {
            const auto& tile = SAFE_VECTOR_ACCESS(m_tiles, tile_id, "TileManager::GetStats tile access");
            if (tile.allocated) {
                stats.memory_used += tile.data.size();
            }
        }
    }
    
    return stats;
}

} // namespace video_core