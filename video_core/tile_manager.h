#pragma once

#include <array>
#include <chrono>
#include <cstdint>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

class CommandProcessor;
class GNMRegisterState;

/**
 * @brief Exception thrown by <PERSON>ileManager for invalid operations.
 */
struct TileManagerException : std::runtime_error {
  explicit TileManagerException(const std::string &msg)
      : std::runtime_error(msg) {}
};

// Tile dimensions
constexpr uint32_t TILE_WIDTH =
    8; ///< Width of a tile in pixels (using 8x8 for GCN architecture)
constexpr uint32_t TILE_HEIGHT =
    8; ///< Height of a tile in pixels (using 8x8 for GCN architecture)

/**
 * @brief Tile modes for surface organization.
 */
enum class TileMode : uint8_t {
  LINEAR = 0,     ///< Linear memory layout
  TILED_1D = 1,   ///< 1D tiled layout (thin)
  TILED_2D = 2,   ///< 2D tiled layout (thick)
  TILED_2B = 3,   ///< 2B tiled layout (2D with bank/pipe rotation)
  TILED_3D = 4,   ///< 3D tiled layout
  TILED_DEPTH = 5 ///< Depth tiled layout
};

/**
 * @brief Tile formats for pixel data.
 */
enum class TileFormat : uint8_t {
  INVALID = 0,
  R8_UNORM = 1,
  R8G8_UNORM = 2,
  R8G8B8A8_UNORM = 3,
  B8G8R8A8_UNORM = 4,
  R16_FLOAT = 5,
  R16G16_FLOAT = 6,
  R16G16B16A16_FLOAT = 7,
  R32_FLOAT = 8,
  R32G32_FLOAT = 9,
  R32G32B32A32_FLOAT = 10,
  D16_UNORM = 11,
  D24_UNORM_S8_UINT = 12,
  D32_FLOAT = 13
};

/**
 * @brief Tile descriptor for tile allocation.
 */
struct TileDescriptor {
  uint32_t width;         ///< Tile width in pixels
  uint32_t height;        ///< Tile height in pixels
  uint32_t bits_per_pixel; ///< Bits per pixel
  TileFormat format;      ///< Pixel format
  TileMode mode;          ///< Tiling mode
};

/**
 * @brief Tile information structure with metrics.
 */
struct TileInfo {
  uint32_t width;         ///< Surface width in pixels
  uint32_t height;        ///< Surface height in pixels
  uint32_t depth;         ///< Surface depth (for 3D)
  TileMode mode;          ///< Tiling mode
  TileFormat format;      ///< Pixel format
  uint32_t bytesPerPixel; ///< Bytes per pixel
  uint32_t pitch;         ///< Pitch in bytes
  uint32_t slice;         ///< Slice size in bytes
  uint64_t gpuAddr;       ///< GPU memory address
  uint64_t cpuAddr;       ///< CPU memory address
  bool isRenderTarget;    ///< Is this a render target?
  bool isDepthStencil;    ///< Is this a depth/stencil buffer?
  mutable uint64_t cacheHits;
  mutable uint64_t cacheMisses;

  /**
   * @brief Serializes the tile info to a stream.
   * @param out Output stream.
   * @details Writes all fields including metrics.
   */
  void Serialize(std::ostream &out) const;

  /**
   * @brief Deserializes the tile info from a stream.
   * @param in Input stream.
   * @throws TileManagerException on deserialization errors.
   */
  void Deserialize(std::istream &in);
};

/**
 * @brief Statistics for tile manager operations.
 */
struct TileManagerStats {
  uint64_t operationCount = 0; ///< Total operations performed
  uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
  uint64_t cacheHits = 0;      ///< Cache hits for surface operations
  uint64_t cacheMisses = 0;    ///< Cache misses for surface operations
  uint64_t errorCount = 0;     ///< Total errors encountered
};

/**
 * @brief Cache entry for tiled surface data.
 */
struct SurfaceCacheEntry {
  uint64_t surfaceId;
  std::vector<uint8_t> data;
  mutable uint64_t cacheHits;
  mutable uint64_t cacheMisses;
};

/**
 * @brief Manages tiled surfaces for GPU rendering.
 * @details Handles creation, manipulation, and conversion of tiled surfaces,
 * with thread-safe operations. Integrates with GNMRegisterState for
 * render target registers, CommandProcessor for PM4 packet processing,
 * and ShaderEmulator for tiled surface access. Supports surface
 * caching, serialization with versioning, and multi-core diagnostics.
 */
class TileManager {
public:
  TileManager(GNMRegisterState &gnmState, CommandProcessor &commandProcessor)
      : m_gnmState(gnmState), m_commandProcessor(commandProcessor) {}

  // Delete copy and move operations since we hold references
  TileManager(const TileManager &) = delete;
  TileManager &operator=(const TileManager &) = delete;
  TileManager(TileManager &&) = delete;
  TileManager &operator=(TileManager &&) = delete;

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~TileManager();

  /**
   * @brief Initializes the tile manager.
   * @return True on success, false on failure.
   * @throws TileManagerException on initialization errors.
   * @details Resets surfaces and metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the tile manager.
   * @details Clears surfaces and metrics. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Creates a tiled surface.
   * @param width Surface width in pixels.
   * @param height Surface height in pixels.
   * @param depth Surface depth (for 3D).
   * @param mode Tiling mode.
   * @param format Pixel format.
   * @param isRenderTarget Is this a render target?
   * @param isDepthStencil Is this a depth/stencil buffer?
   * @return Surface ID.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe. Updates metrics and cache.
   */
  uint64_t CreateTiledSurface(uint32_t width, uint32_t height, uint32_t depth,
                              TileMode mode, TileFormat format,
                              bool isRenderTarget = false,
                              bool isDepthStencil = false);

  /**
   * @brief Destroys a tiled surface.
   * @param surfaceId Surface ID.
   * @throws TileManagerException on invalid surface ID.
   * @details Thread-safe. Updates metrics and cache.
   */
  void DestroyTiledSurface(uint64_t surfaceId);

  /**
   * @brief Gets tile information for a surface.
   * @param surfaceId Surface ID.
   * @return Pointer to tile info, or nullptr if invalid.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  const TileInfo *GetTileInfo(uint64_t surfaceId) const;

  /**
   * @brief Converts linear data to tiled format.
   * @param surfaceId Surface ID.
   * @param linearData Input linear data.
   * @param linearSize Size of linear data.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe. Uses cache if available.
   */
  void LinearToTiled(uint64_t surfaceId, const void *linearData,
                     size_t linearSize);

  /**
   * @brief Converts tiled data to linear format.
   * @param surfaceId Surface ID.
   * @param linearData Output linear data.
   * @param linearSize Size of linear buffer.
   * @throws TileManagerException on invalid parameters.
   * @details Thread-safe (read-only). Uses cache if available.
   */
  void TiledToLinear(uint64_t surfaceId, void *linearData,
                     size_t linearSize) const;

  /**
   * @brief Clears a tiled surface with a color.
   * @param surfaceId Surface ID.
   * @param color RGBA color values.
   * @throws TileManagerException on invalid surface ID or format.
   * @details Thread-safe. Updates cache and metrics.
   */
  void ClearTiledSurface(uint64_t surfaceId, const float color[4]);

  /**
   * @brief Copies one tiled surface to another.
   * @param dstSurfaceId Destination surface ID.
   * @param srcSurfaceId Source surface ID.
   * @throws TileManagerException on invalid or incompatible surfaces.
   * @details Thread-safe. Updates cache and metrics.
   */
  void CopyTiledSurface(uint64_t dstSurfaceId, uint64_t srcSurfaceId);

  /**
   * @brief Gets bytes per pixel for a format.
   * @param format Pixel format.
   * @return Bytes per pixel.
   * @throws TileManagerException on invalid format.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  uint32_t GetBytesPerPixel(TileFormat format);

  /**
   * @brief Gets the current tile's position and size.
   * @param x Output tile x-coordinate.
   * @param y Output tile y-coordinate.
   * @param width Output tile width.
   * @param height Output tile height.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  void GetCurrentTile(uint32_t &x, uint32_t &y, uint32_t &width,
                      uint32_t &height) const;

  /**
   * @brief Advances to the next tile.
   * @details Thread-safe. Updates metrics.
   */
  void NextTile();

  /**
   * @brief Gets the total number of tiles across all surfaces.
   * @return Total tile count.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  size_t GetTotalTiles() const;

  /**
   * @brief Retrieves cached surface data.
   * @param surfaceId Surface ID.
   * @param data Output cached data (if available).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedSurfaceData(uint64_t surfaceId,
                            std::vector<uint8_t> &data) const;

  /**
   * @brief Clears the surface cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearSurfaceCache();

  /**
   * @brief Tile statistics structure.
   */
  struct TileStats {
    size_t total_tiles = 0;     ///< Total number of tiles
    size_t allocated_tiles = 0; ///< Number of allocated tiles
    size_t free_tiles = 0;      ///< Number of free tiles
    size_t memory_used = 0;     ///< Memory used by tiles
  };

  /**
   * @brief Retrieves tile manager statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  TileManagerStats GetStats() const;

  /**
   * @brief Gets tile statistics.
   * @return Current tile statistics.
   * @details Thread-safe (read-only).
   */
  TileStats GetTileStats() const;

  /**
   * @brief Saves the tile manager state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the tile manager state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws TileManagerException on invalid state data.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the tile mode for an existing surface.
   * @param surfaceId Surface ID.
   * @param tileMode New tile mode to set.
   * @return True on success, false on failure.
   * @throws TileManagerException on invalid surface ID or mode.
   * @details Thread-safe. Updates metrics and cache.
   */
  bool SetTileMode(uint64_t surfaceId, uint32_t tileMode);

  /**
   * @brief Maps CPU memory to tiled GPU memory.
   * @param cpuAddress CPU address of the memory to map.
   * @param size Size of the memory to map.
   * @param tileMode Tiling mode to use.
   * @return GPU address of the mapped memory.
   * @throws TileManagerException on invalid parameters or mapping failure.
   * @details Thread-safe. Updates metrics and cache.
   */
  uint64_t MapTiledMemory(uint64_t cpuAddress, size_t size, uint32_t tileMode);

private:
  /**
   * @brief Tiled surface structure with metrics.
   */
  struct TiledSurface {
    TileInfo info;                    ///< Surface metadata
    std::vector<uint8_t> data;        ///< Surface data
    bool active = false;              ///< Is the surface active?
    mutable uint64_t cacheHits = 0;   ///< Cache hits for this surface
    mutable uint64_t cacheMisses = 0; ///< Cache misses for this surface

    /**
     * @brief Serializes the tiled surface to a stream.
     * @param out Output stream.
     */
    void Serialize(std::ostream &out) const;

    /**
     * @brief Deserializes the tiled surface from a stream.
     * @param in Input stream.
     * @throws TileManagerException on deserialization errors.
     */
    void Deserialize(std::istream &in);
  };

  GNMRegisterState &m_gnmState;         ///< Reference to GNM register state
  CommandProcessor &m_commandProcessor; ///< Reference to command processor
  std::unordered_map<uint64_t, TiledSurface> m_surfaces; ///< Active surfaces
  mutable std::shared_mutex m_surfaceMutex; ///< Mutex for thread safety
  uint64_t m_nextSurfaceId = 1;             ///< Next surface ID
  uint64_t m_currentSurfaceId = 0;          ///< Current active surface ID
  uint32_t m_currentTileX = 0;              ///< Current tile X position
  uint32_t m_currentTileY = 0;              ///< Current tile Y position
  uint32_t m_tilesX = 0;                    ///< Number of tiles in X
  uint32_t m_tilesY = 0;                    ///< Number of tiles in Y
  mutable TileManagerStats m_stats;
  std::unordered_map<uint64_t, SurfaceCacheEntry>
      m_surfaceCache; ///< Surface data cache

  // Additional members for tile management
  struct Tile {
    std::vector<uint8_t> data;
    TileDescriptor descriptor;
    bool allocated = false;
    std::chrono::steady_clock::time_point last_access;
  };

  std::vector<Tile> m_tiles;                ///< Tile storage
  std::unordered_map<uint32_t, Tile> m_tile_cache; ///< Tile cache
  std::vector<uint32_t> m_active_tiles;     ///< Active tile IDs
  size_t m_max_tiles = 1024;               ///< Maximum number of tiles
  mutable std::shared_mutex m_mutex;       ///< Mutex for tile operations

  // Tiling conversion helpers
  void LinearToTiled1D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear1D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled2D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear2D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled2B(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear2B(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiled3D(const TileInfo &info, const void *linearData,
                       void *tiledData) const;
  void TiledToLinear3D(const TileInfo &info, const void *tiledData,
                       void *linearData) const;
  void LinearToTiledDepth(const TileInfo &info, const void *linearData,
                          void *tiledData) const;
  void TiledToLinearDepth(const TileInfo &info, const void *tiledData,
                          void *linearData) const;
  uint64_t GetGcnTiledAddress(const TileInfo &info, uint32_t x, uint32_t y,
                              uint32_t z) const;

  /**
   * @brief Calculates tile layout for a surface.
   * @param info Tile information (updated with pitch and slice).
   * @throws TileManagerException on invalid parameters.
   */
  void CalculateTileLayout(TileInfo &info);

  /**
   * @brief Internal helper to get bytes per pixel without locking.
   * @param format Pixel format.
   * @return Bytes per pixel.
   * @throws TileManagerException on invalid format.
   * @details Used when mutex is already held to avoid deadlock.
   */
  uint32_t GetBytesPerPixelInternal(TileFormat format);
};

} // namespace ps4
